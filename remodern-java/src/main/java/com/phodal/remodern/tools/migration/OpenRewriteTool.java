package com.phodal.remodern.tools.migration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.phodal.remodern.core.AbstractMcpTool;
import com.phodal.remodern.core.McpToolException;
import com.phodal.remodern.core.McpToolResult;
import org.openrewrite.*;
import org.openrewrite.config.Environment;
import org.openrewrite.java.JavaParser;
import org.openrewrite.java.tree.J;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MCP Tool for AST migration and refactoring using OpenRewrite
 */
public class OpenRewriteTool extends AbstractMcpTool {
    
    private final Environment environment;
    
    public OpenRewriteTool() {
        super("openrewrite-tool", 
              "Perform AST migration and refactoring using OpenRewrite. Supports code transformations, migrations, and refactoring.",
              "migration");
        
        // Initialize OpenRewrite environment
        this.environment = Environment.builder()
                .scanRuntimeClasspath()
                .build();
    }
    
    @Override
    public JsonNode getInputSchema() {
        ObjectNode schema = createBaseSchema();
        ObjectNode properties = (ObjectNode) schema.get("properties");
        ArrayNode required = (ArrayNode) schema.get("required");
        
        // Operation type
        ObjectNode operationProperty = objectMapper.createObjectNode();
        operationProperty.put("type", "string");
        operationProperty.put("description", "Type of OpenRewrite operation to perform");
        ArrayNode operationEnum = objectMapper.createArrayNode();
        operationEnum.add("recipe").add("visitor").add("refactor").add("migrate");
        operationProperty.set("enum", operationEnum);
        properties.set("operation", operationProperty);
        required.add("operation");
        
        // Source files or directory
        ObjectNode sourceProperty = objectMapper.createObjectNode();
        sourceProperty.put("type", "string");
        sourceProperty.put("description", "Source file path or directory to process");
        properties.set("source", sourceProperty);
        required.add("source");
        
        // Recipe name (for recipe operation)
        ObjectNode recipeProperty = objectMapper.createObjectNode();
        recipeProperty.put("type", "string");
        recipeProperty.put("description", "Name of the OpenRewrite recipe to apply");
        properties.set("recipe", recipeProperty);
        
        // Custom visitor class (for visitor operation)
        ObjectNode visitorProperty = objectMapper.createObjectNode();
        visitorProperty.put("type", "string");
        visitorProperty.put("description", "Fully qualified name of custom visitor class");
        properties.set("visitor", visitorProperty);
        
        // Refactoring rules (for refactor operation)
        ObjectNode rulesProperty = objectMapper.createObjectNode();
        rulesProperty.put("type", "array");
        rulesProperty.put("description", "List of refactoring rules to apply");
        properties.set("rules", rulesProperty);
        
        // Migration target (for migrate operation)
        ObjectNode migrationTargetProperty = objectMapper.createObjectNode();
        migrationTargetProperty.put("type", "string");
        migrationTargetProperty.put("description", "Target framework/version for migration");
        properties.set("migrationTarget", migrationTargetProperty);
        
        // Output directory
        ObjectNode outputDirProperty = objectMapper.createObjectNode();
        outputDirProperty.put("type", "string");
        outputDirProperty.put("description", "Output directory for transformed files");
        properties.set("outputDir", outputDirProperty);
        
        // Dry run mode
        ObjectNode dryRunProperty = objectMapper.createObjectNode();
        dryRunProperty.put("type", "boolean");
        dryRunProperty.put("description", "Whether to perform a dry run without writing changes");
        dryRunProperty.put("default", false);
        properties.set("dryRun", dryRunProperty);
        
        // Include patterns
        ObjectNode includeProperty = objectMapper.createObjectNode();
        includeProperty.put("type", "array");
        includeProperty.put("description", "File patterns to include");
        ObjectNode includeItems = objectMapper.createObjectNode();
        includeItems.put("type", "string");
        includeProperty.set("items", includeItems);
        properties.set("include", includeProperty);
        
        // Exclude patterns
        ObjectNode excludeProperty = objectMapper.createObjectNode();
        excludeProperty.put("type", "array");
        excludeProperty.put("description", "File patterns to exclude");
        ObjectNode excludeItems = objectMapper.createObjectNode();
        excludeItems.put("type", "string");
        excludeProperty.set("items", excludeItems);
        properties.set("exclude", excludeProperty);
        
        return schema;
    }
    
    @Override
    protected McpToolResult doExecute(Map<String, Object> parameters) throws McpToolException {
        String operation = getRequiredParameter(parameters, "operation", String.class);
        String source = getRequiredParameter(parameters, "source", String.class);
        String outputDir = getOptionalParameter(parameters, "outputDir", null, String.class);
        boolean dryRun = getOptionalParameter(parameters, "dryRun", false, Boolean.class);
        
        validateNotEmpty(source, "source");
        
        try {
            return switch (operation.toLowerCase()) {
                case "recipe" -> executeRecipe(parameters, source, outputDir, dryRun);
                case "visitor" -> executeVisitor(parameters, source, outputDir, dryRun);
                case "refactor" -> executeRefactor(parameters, source, outputDir, dryRun);
                case "migrate" -> executeMigration(parameters, source, outputDir, dryRun);
                default -> throw new McpToolException(getName(), "INVALID_OPERATION", 
                        "Unsupported operation: " + operation);
            };
        } catch (IOException e) {
            throw new McpToolException(getName(), "IO_ERROR", 
                    "Failed to process files", e);
        }
    }
    
    private McpToolResult executeRecipe(Map<String, Object> parameters, String source, 
                                       String outputDir, boolean dryRun) throws McpToolException, IOException {
        String recipeName = getRequiredParameter(parameters, "recipe", String.class);
        validateNotEmpty(recipeName, "recipe");
        
        // Find and load the recipe
        Recipe recipe = environment.activateRecipes(recipeName);
        if (recipe == null) {
            throw new McpToolException(getName(), "RECIPE_NOT_FOUND", 
                    "Recipe not found: " + recipeName);
        }
        
        // Parse source files
        List<SourceFile> sourceFiles = parseSourceFiles(source, parameters);
        
        // Execute recipe
        List<Result> results = recipe.run(sourceFiles);
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("recipeName", recipeName);
        metadata.put("filesProcessed", sourceFiles.size());
        metadata.put("changesFound", results.size());
        
        if (!dryRun && outputDir != null) {
            writeResults(results, outputDir);
            metadata.put("outputDir", outputDir);
        }
        
        // Collect change summary
        List<String> changes = results.stream()
                .map(result -> result.getBefore().getSourcePath() + " -> " + 
                              (result.getAfter() != null ? "modified" : "deleted"))
                .collect(Collectors.toList());
        metadata.put("changes", changes);
        
        return McpToolResult.success("Recipe executed successfully", metadata);
    }
    
    private McpToolResult executeVisitor(Map<String, Object> parameters, String source, 
                                        String outputDir, boolean dryRun) throws McpToolException, IOException {
        String visitorClassName = getRequiredParameter(parameters, "visitor", String.class);
        validateNotEmpty(visitorClassName, "visitor");
        
        try {
            // Load custom visitor class
            @SuppressWarnings("unchecked")
            Class<? extends TreeVisitor<?, ExecutionContext>> visitorClass = 
                    (Class<? extends TreeVisitor<?, ExecutionContext>>) Class.forName(visitorClassName);
            TreeVisitor<?, ExecutionContext> visitor = visitorClass.getDeclaredConstructor().newInstance();
            
            // Parse source files
            List<SourceFile> sourceFiles = parseSourceFiles(source, parameters);
            
            // Apply visitor
            ExecutionContext ctx = new InMemoryExecutionContext();
            List<SourceFile> transformed = new ArrayList<>();
            
            for (SourceFile sourceFile : sourceFiles) {
                SourceFile result = (SourceFile) visitor.visit(sourceFile, ctx);
                if (result != null) {
                    transformed.add(result);
                }
            }
            
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("visitorClass", visitorClassName);
            metadata.put("filesProcessed", sourceFiles.size());
            metadata.put("filesTransformed", transformed.size());
            
            if (!dryRun && outputDir != null) {
                writeTransformedFiles(transformed, outputDir);
                metadata.put("outputDir", outputDir);
            }
            
            return McpToolResult.success("Visitor executed successfully", metadata);
            
        } catch (Exception e) {
            throw new McpToolException(getName(), "VISITOR_ERROR", 
                    "Failed to execute visitor: " + e.getMessage(), e);
        }
    }
    
    private McpToolResult executeRefactor(Map<String, Object> parameters, String source, 
                                         String outputDir, boolean dryRun) throws McpToolException, IOException {
        @SuppressWarnings("unchecked")
        List<String> rules = getRequiredParameter(parameters, "rules", List.class);
        
        if (rules.isEmpty()) {
            throw new McpToolException(getName(), "NO_RULES", "No refactoring rules specified");
        }
        
        // Create a composite recipe from rules
        Recipe.Builder recipeBuilder = Recipe.builder("custom-refactor");
        for (String rule : rules) {
            Recipe ruleRecipe = environment.activateRecipes(rule);
            if (ruleRecipe != null) {
                recipeBuilder.recipeList(ruleRecipe);
            }
        }
        Recipe compositeRecipe = recipeBuilder.build();
        
        // Parse source files
        List<SourceFile> sourceFiles = parseSourceFiles(source, parameters);
        
        // Execute refactoring
        List<Result> results = compositeRecipe.run(sourceFiles);
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("rules", rules);
        metadata.put("filesProcessed", sourceFiles.size());
        metadata.put("changesFound", results.size());
        
        if (!dryRun && outputDir != null) {
            writeResults(results, outputDir);
            metadata.put("outputDir", outputDir);
        }
        
        return McpToolResult.success("Refactoring completed successfully", metadata);
    }
    
    private McpToolResult executeMigration(Map<String, Object> parameters, String source, 
                                          String outputDir, boolean dryRun) throws McpToolException, IOException {
        String migrationTarget = getRequiredParameter(parameters, "migrationTarget", String.class);
        validateNotEmpty(migrationTarget, "migrationTarget");
        
        // Find migration recipes based on target
        List<Recipe> migrationRecipes = findMigrationRecipes(migrationTarget);
        if (migrationRecipes.isEmpty()) {
            throw new McpToolException(getName(), "NO_MIGRATION_RECIPES", 
                    "No migration recipes found for target: " + migrationTarget);
        }
        
        // Create composite migration recipe
        Recipe.Builder migrationBuilder = Recipe.builder("migration-" + migrationTarget);
        migrationRecipes.forEach(migrationBuilder::recipeList);
        Recipe migrationRecipe = migrationBuilder.build();
        
        // Parse source files
        List<SourceFile> sourceFiles = parseSourceFiles(source, parameters);
        
        // Execute migration
        List<Result> results = migrationRecipe.run(sourceFiles);
        
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("migrationTarget", migrationTarget);
        metadata.put("recipesApplied", migrationRecipes.size());
        metadata.put("filesProcessed", sourceFiles.size());
        metadata.put("changesFound", results.size());
        
        if (!dryRun && outputDir != null) {
            writeResults(results, outputDir);
            metadata.put("outputDir", outputDir);
        }
        
        return McpToolResult.success("Migration completed successfully", metadata);
    }
    
    private List<SourceFile> parseSourceFiles(String source, Map<String, Object> parameters) 
            throws IOException, McpToolException {
        Path sourcePath = Paths.get(source);
        
        if (!Files.exists(sourcePath)) {
            throw new McpToolException(getName(), "SOURCE_NOT_FOUND", 
                    "Source path not found: " + source);
        }
        
        @SuppressWarnings("unchecked")
        List<String> includePatterns = getOptionalParameter(parameters, "include", new ArrayList<>(), List.class);
        @SuppressWarnings("unchecked")
        List<String> excludePatterns = getOptionalParameter(parameters, "exclude", new ArrayList<>(), List.class);
        
        JavaParser javaParser = JavaParser.fromJavaVersion()
                .classpath(JavaParser.runtimeClasspath())
                .build();
        
        List<Path> javaFiles = new ArrayList<>();
        
        if (Files.isDirectory(sourcePath)) {
            // Collect Java files from directory
            Files.walk(sourcePath)
                    .filter(Files::isRegularFile)
                    .filter(path -> path.toString().endsWith(".java"))
                    .filter(path -> matchesPatterns(path, includePatterns, excludePatterns))
                    .forEach(javaFiles::add);
        } else if (sourcePath.toString().endsWith(".java")) {
            javaFiles.add(sourcePath);
        }
        
        if (javaFiles.isEmpty()) {
            throw new McpToolException(getName(), "NO_JAVA_FILES", 
                    "No Java files found in source: " + source);
        }
        
        return javaParser.parse(javaFiles, sourcePath);
    }
    
    private boolean matchesPatterns(Path path, List<String> includePatterns, List<String> excludePatterns) {
        String pathString = path.toString();
        
        // Check exclude patterns first
        for (String exclude : excludePatterns) {
            if (pathString.matches(exclude.replace("*", ".*"))) {
                return false;
            }
        }
        
        // If no include patterns, include by default
        if (includePatterns.isEmpty()) {
            return true;
        }
        
        // Check include patterns
        for (String include : includePatterns) {
            if (pathString.matches(include.replace("*", ".*"))) {
                return true;
            }
        }
        
        return false;
    }
    
    private void writeResults(List<Result> results, String outputDir) throws IOException {
        Path outputPath = Paths.get(outputDir);
        Files.createDirectories(outputPath);
        
        for (Result result : results) {
            if (result.getAfter() != null) {
                Path targetFile = outputPath.resolve(result.getAfter().getSourcePath());
                Files.createDirectories(targetFile.getParent());
                Files.writeString(targetFile, result.getAfter().printAll());
            }
        }
    }
    
    private void writeTransformedFiles(List<SourceFile> files, String outputDir) throws IOException {
        Path outputPath = Paths.get(outputDir);
        Files.createDirectories(outputPath);
        
        for (SourceFile file : files) {
            Path targetFile = outputPath.resolve(file.getSourcePath());
            Files.createDirectories(targetFile.getParent());
            Files.writeString(targetFile, file.printAll());
        }
    }
    
    private List<Recipe> findMigrationRecipes(String migrationTarget) {
        // This is a simplified implementation
        // In a real scenario, you would have a mapping of migration targets to recipes
        return environment.listRecipes().stream()
                .filter(recipe -> recipe.getName().toLowerCase().contains(migrationTarget.toLowerCase()))
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean supportsOperation(String operation) {
        return switch (operation.toLowerCase()) {
            case "recipe", "visitor", "refactor", "migrate", "ast-migration", "code-transformation" -> true;
            default -> false;
        };
    }
}
