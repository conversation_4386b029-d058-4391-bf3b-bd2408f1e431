[{"name": "com.phodal.remodern.cli.ReModernCli", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true}, {"name": "com.phodal.remodern.cli.ReModernCli$InfoCommand", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "fields": [{"name": "toolName"}]}, {"name": "com.phodal.remodern.cli.ReModernCli$ListCommand", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "fields": [{"name": "category"}, {"name": "verbose"}]}, {"name": "com.phodal.remodern.cli.ReModernCli$RunCommand", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "fields": [{"name": "dryRun"}, {"name": "outputFormat"}, {"name": "parameterFile"}, {"name": "parameters"}, {"name": "toolName"}]}, {"name": "picocli.CommandLine$AutoHelpMixin", "allDeclaredConstructors": true, "allPublicConstructors": true, "allDeclaredMethods": true, "allPublicMethods": true, "fields": [{"name": "helpRequested"}, {"name": "versionRequested"}]}]