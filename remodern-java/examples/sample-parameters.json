{"astCodeGenExample": {"type": "class", "name": "UserService", "packageName": "com.example.service", "modifiers": ["public"], "interfaces": ["UserRepository"], "fields": [{"name": "userDao", "type": "UserDao", "modifiers": ["private", "final"]}], "methods": [{"name": "findById", "returnType": "User", "modifiers": ["public"], "parameters": [{"name": "id", "type": "<PERSON>"}], "body": "return userDao.findById(id)"}, {"name": "save", "returnType": "User", "modifiers": ["public"], "parameters": [{"name": "user", "type": "User"}], "body": "return userDao.save(user)"}], "annotations": ["@Service", "@Transactional"]}, "templateCodeGenExample": {"template": "package ${packageName};\n\nimport javax.persistence.*;\nimport java.io.Serializable;\n\n@Entity\n@Table(name = \"${tableName}\")\npublic class ${className} implements Serializable {\n\n<#list fields as field>\n    @Column(name = \"${field.columnName}\")\n    private ${field.type} ${field.name};\n\n</#list>\n\n    // Default constructor\n    public ${className}() {}\n\n<#list fields as field>\n    public ${field.type} get${field.name?cap_first}() {\n        return ${field.name};\n    }\n\n    public void set${field.name?cap_first}(${field.type} ${field.name}) {\n        this.${field.name} = ${field.name};\n    }\n\n</#list>\n}", "templateType": "content", "dataModel": {"packageName": "com.example.entity", "className": "User", "tableName": "users", "fields": [{"name": "id", "type": "<PERSON>", "columnName": "id"}, {"name": "username", "type": "String", "columnName": "username"}, {"name": "email", "type": "String", "columnName": "email"}]}}, "javaParseExample": {"source": "src/main/java", "analysisType": "full", "includeMethodBodies": true, "includeComments": true, "include": ["*.java"], "exclude": ["*Test.java", "*IT.java"]}, "jspParseExample": {"source": "src/main/webapp", "analysisType": "full", "extractContent": true, "validateSyntax": true, "include": ["*.jsp", "*.jspx"], "exclude": ["WEB-INF/**"]}, "bytecodeAnalysisExample": {"source": "target/classes", "analysisType": "full", "includeMethodBytecode": false, "includeDebug": true, "analyzeDependencies": true, "classFilter": "com\\.example\\..*"}, "openRewriteRecipeExample": {"operation": "recipe", "source": "src/main/java", "recipe": "org.openrewrite.java.migrate.JavaVersion11", "outputDir": "migrated-src", "dryRun": false}, "openRewriteMigrationExample": {"operation": "migrate", "source": "src/main/java", "migrationTarget": "spring-boot-3", "outputDir": "migrated-src", "dryRun": true, "include": ["*.java"], "exclude": ["*Test.java"]}}