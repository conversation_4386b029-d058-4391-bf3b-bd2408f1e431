#!/bin/bash

# ReModern Java - Example Usage Scripts
# This script demonstrates how to use the various MCP tools

set -e

JAR_FILE="target/remodern-java-1.0.0-SNAPSHOT.jar"
EXAMPLES_DIR="examples"

echo "ReModern Java - Example Usage"
echo "============================="

# Check if JAR file exists
if [ ! -f "$JAR_FILE" ]; then
    echo "Error: JAR file not found. Please run 'mvn clean package' first."
    exit 1
fi

# Create output directories
mkdir -p output/generated
mkdir -p output/analysis

echo ""
echo "1. Listing all available tools..."
java -jar "$JAR_FILE" list --verbose

echo ""
echo "2. Getting information about ast-code-gen tool..."
java -jar "$JAR_FILE" info ast-code-gen

echo ""
echo "3. Generating a Java class using AST code generation..."
java -jar "$JAR_FILE" run ast-code-gen \
  -p type=class \
  -p name=ExampleClass \
  -p packageName=com.example.generated \
  -p outputDir=output/generated \
  -p modifiers=public \
  --output text

echo ""
echo "4. Generating code using template (if sample file exists)..."
if [ -f "src/main/java/com/example/Sample.java" ]; then
    java -jar "$JAR_FILE" run template-code-gen \
      -f "$EXAMPLES_DIR/sample-parameters.json" \
      -p templateCodeGenExample \
      -p outputFile=output/generated/TemplateUser.java \
      --output text
else
    echo "Skipping template generation - no sample Java file found"
fi

echo ""
echo "5. Parsing Java source files..."
if [ -d "src/main/java" ]; then
    java -jar "$JAR_FILE" run java-parse-tool \
      -p source=src/main/java \
      -p analysisType=structure \
      --output text > output/analysis/java-analysis.txt
    echo "Java analysis saved to output/analysis/java-analysis.txt"
else
    echo "Skipping Java parsing - no src/main/java directory found"
fi

echo ""
echo "6. Analyzing bytecode (if classes exist)..."
if [ -d "target/classes" ]; then
    java -jar "$JAR_FILE" run bytecode-tool \
      -p source=target/classes \
      -p analysisType=structure \
      --output text > output/analysis/bytecode-analysis.txt
    echo "Bytecode analysis saved to output/analysis/bytecode-analysis.txt"
else
    echo "Skipping bytecode analysis - no target/classes directory found"
fi

echo ""
echo "7. Parsing JSP files (if any exist)..."
if [ -d "src/main/webapp" ] && find src/main/webapp -name "*.jsp" -o -name "*.jspx" | grep -q .; then
    java -jar "$JAR_FILE" run jsp-parse-tool \
      -p source=src/main/webapp \
      -p analysisType=structure \
      --output text > output/analysis/jsp-analysis.txt
    echo "JSP analysis saved to output/analysis/jsp-analysis.txt"
else
    echo "Skipping JSP parsing - no JSP files found"
fi

echo ""
echo "8. Demonstrating dry-run mode with OpenRewrite..."
if [ -d "src/main/java" ]; then
    java -jar "$JAR_FILE" run openrewrite-tool \
      -p operation=refactor \
      -p source=src/main/java \
      -p rules=org.openrewrite.java.format.AutoFormat \
      -p dryRun=true \
      --output text
else
    echo "Skipping OpenRewrite demo - no src/main/java directory found"
fi

echo ""
echo "Examples completed! Check the output/ directory for generated files and analysis results."
echo ""
echo "Generated files:"
find output -type f -name "*.java" 2>/dev/null || echo "No Java files generated"
echo ""
echo "Analysis files:"
find output -type f -name "*.txt" 2>/dev/null || echo "No analysis files generated"

echo ""
echo "To run individual tools, use:"
echo "  java -jar $JAR_FILE run <tool-name> [parameters]"
echo ""
echo "To see all available tools:"
echo "  java -jar $JAR_FILE list"
echo ""
echo "To get help for a specific tool:"
echo "  java -jar $JAR_FILE info <tool-name>"
